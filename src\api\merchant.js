import request from '@/libs/request';

/**
 * @description 商家类型 -- 列表
 * @param {Object} param params {Object} 传值参数
 */
export function merchantTypeListApi(params) {
  return request({
    url: 'merchant/type',
    method: 'get',
    params,
  });
}

/**
 * @description 商家类型 -- 添加表单
 */
export function merchantTypeCreateApi(id) {
  return request({
    url: 'merchant/type/create/'+id,
    method: 'get',
  });
}

/**
 * @description 商家类型 -- 编辑表单
 * @param {Number} param id {Number} 商家类型id
 */
export function merchantTypeEditApi(id) {
  return request({
    url: `merchant/type/${id}/edit`,
    method: 'get',
  });
}

/**
 * @description 商家类型 -- 保存
 * @param {Object} param data {Object} 传值参数
 */
export function merchantTypeSaveApi(data) {
  return request({
    url: `merchant/type/${data.id}`,
    method: 'post',
    data,
  });
}

/**
 * @description 商家类型 -- 删除
 * @param {Number} param id {Number} 商家类型id
 */
export function merchantTypeDeleteApi(id) {
  return request({
    url: `merchant/type/${id}`,
    method: 'delete',
  });
}

/**
 * @description 商家类型 -- 修改状态
 * @param {Object} param data {Object} 传值参数
 */
export function merchantTypeStatusApi(data) {
  return request({
    url: `merchant/type/status/${data.id}`,
    method: 'put',
    data,
  });
}

/**
 * @description 商家分类 -- 列表
 * @param {Object} param params {Object} 传值参数
 */
export function merchantClassifyListApi(params) {
  return request({
    url: 'merchant/classify',
    method: 'get',
    params,
  });
}

/**
 * @description 商家分类 -- 添加表单
 */
export function merchantClassifyCreateApi(id) {
  return request({
    url: 'merchant/classify/create/'+id,
    method: 'get',
  });
}

/**
 * @description 商家分类 -- 编辑表单
 * @param {Number} param id {Number} 商家分类id
 */
export function merchantClassifyEditApi(id) {
  return request({
    url: `merchant/classify/${id}/edit`,
    method: 'get',
  });
}

/**
 * @description 商家分类 -- 保存
 * @param {Object} param data {Object} 传值参数
 */
export function merchantClassifySaveApi(data) {
  return request({
    url: `merchant/classify/${data.id}`,
    method: 'post',
    data,
  });
}

/**
 * @description 商家分类 -- 删除
 * @param {Number} param id {Number} 商家分类id
 */
export function merchantClassifyDeleteApi(id) {
  return request({
    url: `merchant/classify/${id}`,
    method: 'delete',
  });
}
