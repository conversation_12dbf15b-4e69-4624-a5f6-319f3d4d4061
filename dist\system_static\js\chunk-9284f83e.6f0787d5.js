(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-9284f83e"],{"348d":function(e,t,a){},8065:function(e,t,a){"use strict";a.r(t);var s=a("2909"),n=a("c7eb"),r=a("1da1"),i=a("5530"),o=(a("d9e2"),a("8ba4"),a("a9e3"),a("ac1f"),a("00b4"),a("d81d"),a("14d9"),a("c740"),a("b64b"),a("99af"),a("d3b7"),a("add5")),c=a.n(o),d=a("f8b7"),u=(o=a("2f62"),a("3f2a")),l=a("d708"),f=a("c276"),h=(a("90e7"),a("d468"));i={name:"table_list",components:{},data:function(){return{batchShipmentModal:!1,expressUrl:l.a.apiBaseURL+"/file/upload/1",header:{},delfromData:{},modal:!1,orderList:[],orderCards:[],loading:!1,orderId:0,total_num:0,virtual_type:0,status:0,pay_type:"",total:0,page:{page:1,limit:15},data:[],FromData:null,orderDatalist:null,selectedIds:[],currentTab:"",spinShow:!1,tablists:{all:"0",general:"0",pink:"0",seckill:"0",bargain:"0",advance:"0"},writeOffRules:{code:[{validator:function(e,t,a){if(!t)return a(new Error("请填写核销码"));Number.isInteger(t)&&/\b\d{12}\b/.test(t)?a():a(new Error("请填写12位数字"))},trigger:"blur",required:!0}]},writeOffFrom:{code:"",confirm:0},modals2:!1}},computed:Object(i.a)({},Object(o.d)("order",["orderPayType","orderStatus","orderTime","real_name","fieldKey","orderType","delIdList","isDels"])),mounted:function(){},created:function(){this.getTabs(),this.onChangeTabs(""),this.getList(),this.getToken()},watch:{orderType:function(){this.page.page=1,this.getList()}},methods:Object(i.a)(Object(i.a)({},Object(o.c)("order",["onChangeTabs","getIsDel","getisDelIdListl"])),{},{batchShipment:function(){},changeMenu:function(e,t){var a=this;switch(this.orderId=e.id,t){case"1":this.delfromData={title:"修改立即支付",url:"/order/pay_offline/".concat(e.id),method:"post",ids:""},this.$modalSure(this.delfromData).then((function(e){a.$message.success(e.msg),a.$emit("changeGetTabs"),a.getList()})).catch((function(e){a.$message.error(e.msg)}));break;case"2":this.getData(e.id);break;case"4":this.$refs.remarks.modals=!0,this.$refs.remarks.formValidate.remark=e.remark;break;case"5":this.getRefundData(e.id);break;case"8":this.delfromData={title:"修改确认收货",url:"/order/take/".concat(e.id),method:"put",ids:""},this.$modalSure(this.delfromData).then((function(e){a.$message.success(e.msg),a.getList()})).catch((function(e){a.$message.error(e.msg)}));break;case"10":this.delfromData={title:"立即打印订单",info:"您确认打印此订单吗?",url:"/order/print/".concat(e.id),method:"get",ids:""},this.$modalSure(this.delfromData).then((function(e){a.$message.success(e.msg),a.$emit("changeGetTabs"),a.getList()})).catch((function(e){a.$message.error(e.msg)}));break;case"11":this.delfromData={title:"立即打印电子面单",info:"您确认打印此电子面单吗?",url:"/order/order_dump/".concat(e.id),method:"get",ids:""},this.$modalSure(this.delfromData).then((function(e){a.$message.success(e.msg),a.getList()})).catch((function(e){a.$message.error(e.msg)}));break;case"12":this.printImg(e.kuaidi_label);break;case"13":var s=this.$router.resolve({path:"/admin/order/print",query:{id:e.order_id}});window.open(s.href,"_blank");break;default:this.delfromData={title:"删除订单",url:"/order/del/".concat(e.id),method:"DELETE",ids:""},this.delOrder(e,this.delfromData)}},shipmentClear:function(e){this.orderId=e.id,this.$refs.shipment.modals=!0},printImg:function(e){c()({printable:e,type:"image",documentTitle:"快递信息",style:"img{\n          width: 100%;\n          height: 476px;\n        }"})},submitModel:function(){this.getList()},getList:function(e){var t=this;this.page.page=1===e?1:this.page.page,this.loading=!0,Object(d.getJingdongOrderList)({page:this.page.page,limit:this.page.limit}).then(function(){var e=Object(r.a)(Object(n.a)().mark((function e(a){var s;return Object(n.a)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:s=a.data,t.orderList=s.data,t.total=s.count,t.$nextTick((function(){t.setChecked()})),t.$emit("on-changeCards",s.stat),t.loading=!1;case 7:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.loading=!1,t.$message.error(e.msg)}))},handleSelectRow:function(e){var t=this,a=[];e.map((function(e){a.push(e.id)})),this.selectedIds=a,this.$nextTick((function(){t.setChecked()}))},setChecked:function(){var e,t=Object(s.a)(this.selectedIds),a=(this.getisDelIdListl(t),this.$refs.table.objData);for(e in a)this.selectedIds.has(a[e].id)&&(a[e]._isChecked=!0)},isDel:function(e){-1==e.findIndex((function(e){return 0===e.is_del}))?this.getIsDel(1):this.getIsDel(0)},edit:function(e){this.getOrderData(e.id)},delOrder:function(e,t){var a=this;1===e.is_del?this.$modalSure(t).then((function(e){a.$message.success(e.msg),a.getList()})).catch((function(e){a.$message.error(e.msg)})):this.$message.error("您选择的的订单存在用户未删除的订单，无法删除用户未删除的订单！")},getOrderData:function(e){var t=this;Object(d.n)(e).then(function(){var e=Object(r.a)(Object(n.a)().mark((function e(a){return Object(n.a)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.FromData=a.data,t.$refs.edits.modals=!0;case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$message.error(e.msg)}))},getData:function(e){var t=this;Object(d.f)(e).then(function(){var e=Object(r.a)(Object(n.a)().mark((function e(a){return Object(n.a)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.$refs.details.modals=!0,t.orderDatalist=a.data,t.orderDatalist.orderInfo.refund_reason_wap_img)try{t.orderDatalist.orderInfo.refund_reason_wap_img=JSON.parse(t.orderDatalist.orderInfo.refund_reason_wap_img)}catch(e){t.orderDatalist.orderInfo.refund_reason_wap_img=[]}case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$message.error(e.msg)}))},submitFail:function(){this.getList(),this.$emit("changeGetTabs")},getRefundData:function(e){var t=this;this.$modalForm(Object(d.q)(e)).then((function(){t.getList(),t.$emit("changeGetTabs")}))},getRefundIntegral:function(e){var t=this;Object(d.T)(e).then(function(){var e=Object(r.a)(Object(n.a)().mark((function e(a){return Object(n.a)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.FromData=a.data,t.$refs.edits.modals=!0;case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$message.error(e.msg)}))},getNoRefundData:function(e){var t=this;this.$modalForm(Object(d.r)(e)).then((function(){t.getList(),t.$emit("changeGetTabs")}))},sendOrder:function(e){var t=this;e.user_address&&(this.$refs.send.userSendmsg={real_name:e.real_name,user_address:e.user_address,user_phone:e.user_phone}),this.$refs.send.total_num=e.total_num,this.virtual_type=e.virtual_type,this.$refs.send.modals=!0,this.orderId=e.id,this.status=e._status,this.pay_type=e.pay_type,this.$refs.send.getList(),this.$refs.send.getDeliveryList(),this.$nextTick((function(a){t.$refs.send.getCartInfo(e._status,e.id)}))},delivery:function(e){var t=this;Object(d.h)(e.id).then(function(){var e=Object(r.a)(Object(n.a)().mark((function e(a){return Object(n.a)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.FromData=a.data,t.$refs.edits.modals=!0;case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$message.error(e.msg)}))},bindWrite:function(e){var t=this;this.$msgbox({title:"提示",message:"确定要核销该订单吗？",showCancelButton:!0,cancelButtonText:"取消",confirmButtonText:"确定",iconClass:"el-icon-warning",confirmButtonClass:"btn-custom-cancel"}).then((function(){Object(d.Z)(e.order_id).then((function(e){t.$message.success(e.msg),t.getList()})).catch((function(e){t.$message.error(e.msg)}))})).catch((function(){}))},getTabs:function(){var e=this;this.spinShow=!0,this.$store.dispatch("order/getOrderTabs",{data:""}).then((function(t){e.tablists=t.data,e.spinShow=!1})).catch((function(t){e.spinShow=!1,e.$message.error(t.msg)}))},delAll:function(){var e,t=this;0===this.delIdList.length?this.$message.error("请先选择删除的订单！"):this.isDels?(e={ids:this.delIdList},this.$modalSure({title:"删除订单",url:"/order/dels",method:"post",ids:e}).then((function(e){t.$message.success(e.msg),t.$emit("getList")})).catch((function(e){t.$message.error(e.msg)}))):this.$message.error("您选择的的订单存在用户未删除的订单，无法删除用户未删除的订单！")},exportDeliveryList:function(){var e=this;return Object(r.a)(Object(n.a)().mark((function t(){var a,s,r,i,o,c,d;return Object(n.a)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:a=[],s=[],r=[],i="",o={page:1,limit:200},c=0;case 3:if(c<o.page+1)return t.next=6,e.getDeliveryData(o);t.next=20;break;case 6:d=t.sent,i=i||d.filename,s.length||(s=d.fileKey),a.length||(a=d.header),d.export.length?(r=r.concat(d.export),o.page++,t.next=17):t.next=15;break;case 15:return e.$exportExcel(a,s,i,r),t.abrupt("return");case 17:c++,t.next=3;break;case 20:case"end":return t.stop()}}),t)})))()},getDeliveryData:function(e){return new Promise((function(t,a){Object(u.c)(e).then((function(e){t(e.data)}))}))},getToken:function(){this.header["Authori-zation"]="Bearer "+Object(f.c)("token")},upExpress:function(e){var t=this;Object(d.t)({file:e.data.src}).then((function(e){t.$message.success(e.msg),t.getList()})).catch((function(e){t.$message.error(e.msg)}))},exportList:function(){var e=this;return Object(r.a)(Object(n.a)().mark((function t(){var a,s,r,i;return Object(n.a)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:a={page:1,limit:100,status:e.orderStatus,pay_type:e.orderPayType,data:e.orderTime,real_name:e.real_name,field_key:e.fieldKey,type:0===e.orderType?"":e.orderType,ids:e.delIdList},s=[],r={},i=1;case 2:if(i<a.page+1)return t.next=5,e.getExcelData(a);t.next=10;break;case 5:(r=t.sent).export.length&&(s=s.concat(r.export),r.export.length==a.limit)&&a.page++;case 7:i++,t.next=2;break;case 10:Object(h.a)(r.header,r.filename,s,"",r.filename);case 11:case"end":return t.stop()}}),t)})))()},getExcelData:function(e){return new Promise((function(t,a){Object(u.d)(e).then((function(e){t(e.data)}))}))},writeOff:function(){this.modals2=!0},ok:function(e){var t=this;this.writeOffFrom.code?(this.writeOffFrom.confirm=1,Object(d.P)(this.writeOffFrom).then(function(){var a=Object(r.a)(Object(n.a)().mark((function a(s){return Object(n.a)().wrap((function(a){for(;;)switch(a.prev=a.next){case 0:200===s.status?(t.$message.success(s.msg),t.modals2=!1,t.$refs[e].resetFields(),t.$emit("getList",1)):t.$message.error(s.msg);case 1:case"end":return a.stop()}}),a)})));return function(e){return a.apply(this,arguments)}}()).catch((function(e){t.$message.error(e.msg)}))):this.$message.warning("请先验证订单！")},del:function(e){this.modals2=!1,this.writeOffFrom.code="",this.$refs[e].resetFields()},changeModal:function(){this.writeOffFrom.code=""}})},a("feed"),o=a("2877"),a={components:{tableList:Object(o.a)(i,(function(){var e=this,t=e._self._c;return t("div",[t("div",{staticClass:"acea-row"},[e._v("\n    订单列表\n  ")]),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",staticClass:"orderData mt14",attrs:{data:e.orderList,"empty-text":"暂无数据"},on:{select:e.handleSelectRow,"select-all":e.handleSelectRow}},[t("el-table-column",{attrs:{type:"selection",width:"55"}}),t("el-table-column",{attrs:{label:"订单号",width:"200"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",[e._v(e._s(a.row.orderId))])]}}])}),t("el-table-column",{attrs:{label:"商品信息","min-width":"250"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",{staticClass:"tab"},[t("div",{attrs:{slot:"content"},slot:"content"},[t("div",[t("span",[e._v("商品名称：")]),t("span",[e._v(e._s(a.row.skuName||"--"))])]),t("div",[t("span",[e._v("支付价格：")]),t("span",[e._v("¥"+e._s(a.row.price||"--"))])]),t("div",[t("span",[e._v("购买数量：")]),t("span",[e._v(e._s(a.row.skuNum||"--"))])])])])]}}])}),t("el-table-column",{attrs:{label:"提成比例","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",{staticClass:"nickname"},[e._v(e._s(a.row.commissionRate))])]}}])}),t("el-table-column",{attrs:{label:"预估佣金","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(a.row.yj))])]}}])}),t("el-table-column",{attrs:{label:"京享金抵扣金额","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(a.row.giftCouponOcsAmount))])]}}])}),t("el-table-column",{attrs:{label:"下单时间","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(a.row.orderTime))])]}}])}),t("el-table-column",{attrs:{label:"更新时间","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(a.row.modifyTime))])]}}])}),t("el-table-column",{attrs:{label:"订单状态","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(a.row.validCode))])]}}])}),t("el-table-column",{attrs:{label:"完成时间","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(a.row.finishTime||""))])]}}])})],1),t("div",{staticClass:"acea-row row-right page"},[e.total?t("pagination",{attrs:{total:e.total,page:e.page.page,limit:e.page.limit},on:{"update:page":function(t){return e.$set(e.page,"page",t)},"update:limit":function(t){return e.$set(e.page,"limit",t)},pagination:e.getList}}):e._e()],1)],1)}),[],!1,null,"72e40c79",null).exports}},i=Object(o.a)(a,(function(){var e=this._self._c;return e("el-card",{staticClass:"mt16",attrs:{bordered:!1,shadow:"never","body-style":{padding:"0 20px 20px"}}},[e("table-list")],1)}),[],!1,null,"5132d906",null);t.default=i.exports},feed:function(e,t,a){"use strict";a("348d")}}]);