import LayoutMain from '@/layout';
import setting from '@/setting';
let routePre = setting.routePre;

const meta = {
    auth: true,
};

const pre = 'merchant_';

export default {
    path: routePre + '/merchant',
    name: 'merchant',
    header: 'merchant',
    redirect: {
        name: `${pre}list`,
    },
    meta,
    component: LayoutMain,
    children: [
        {
            path: routePre + '/merchant/list',
            name: `${pre}list`,
            header: 'merchant',
            meta: {
                auth: ['admin-merchant-list'],
                title: '商家列表',
                isAffix: false,
            },
            component: () => import('@/pages/merchant/list/index'),
        },
        {
            path: routePre + '/merchant/type',
            name: `${pre}type`,
            header: 'merchant',
            meta: {
                auth: ['admin-merchant-type'],
                title: '商家类型',
                isAffix: false,
            },
            component: () => import('@/pages/merchant/type/index'),
        },
        {
            path: routePre + '/merchant/classify',
            name: `${pre}classify`,
            header: 'merchant',
            meta: {
                auth: ['admin-merchant-classify'],
                title: '商家分类',
                isAffix: false,
            },
            component: () => import('@/pages/merchant/classify/index'),
        },
        {
            path: routePre + '/merchant/apply',
            name: `${pre}apply`,
            header: 'merchant',
            meta: {
                auth: ['admin-merchant-apply'],
                title: '入驻申请',
                isAffix: false,
            },
            component: () => import('@/pages/merchant/apply/index'),
        },
    ],
};
