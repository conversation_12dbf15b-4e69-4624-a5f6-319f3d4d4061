<template>
  <div class="merchant-classify-manager">
    <el-card :bordered="false" shadow="never" class="ivu-mt" :body-style="{ padding: 0 }">
      <div class="padding-add">
        <el-form ref="searchForm" :model="searchForm" inline label-width="80px" label-position="right" @submit.native.prevent>
          <el-form-item label="分类名称：" prop="class_name" label-for="class_name">
            <el-input
              clearable
              placeholder="请输入分类名称"
              v-model="searchForm.class_name"
              class="form_content_width"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <el-card :bordered="false" shadow="never" class="ivu-mt mt16">
      <el-button v-auth="['admin-merchant-classify-create']" type="primary" class="bnt" @click="handleAdd">添加商家分类</el-button>

      <vxe-table
        class="mt14"
        highlight-hover-row
        :loading="loading"
        header-row-class-name="false"
        :data="tableData"
      >
        <vxe-table-column field="id" title="编号" tooltip width="80"></vxe-table-column>
        <vxe-table-column field="class_name" title="分类名称" min-width="200"></vxe-table-column>
        <vxe-table-column field="fee" title="费率" min-width="120">
          <template v-slot="{ row }">
            <span>{{ row.fee }}%</span>
          </template>
        </vxe-table-column>
        <vxe-table-column field="add_time" title="添加时间" min-width="160"></vxe-table-column>
        <vxe-table-column field="operation" title="操作" width="150" fixed="right">
          <template v-slot="{ row, index }">
            <a @click="handleEdit(row)">编辑</a>
            <el-divider direction="vertical"></el-divider>
            <a @click="handleDelete(row, index)">删除</a>
          </template>
        </vxe-table-column>
      </vxe-table>

      <div class="acea-row row-right page">
        <pagination
          v-if="total"
          :total="total"
          :page.sync="searchForm.page"
          :limit.sync="searchForm.limit"
          @pagination="handlePageChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import {
  merchantClassifyListApi,
  merchantClassifyCreateApi,
  merchantClassifyEditApi,
  merchantClassifyDeleteApi
} from '@/api/merchant';

export default {
  name: 'merchant_classify',
  data() {
    return {
      loading: false,
      searchForm: {
        class_name: '',
        page: 1,
        limit: 15,
      },
      total: 0,
      tableData: [],
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    // 获取列表数据
    getList() {
      this.loading = true;


      // 生产环境调用真实API
      merchantClassifyListApi(this.searchForm)
        .then((res) => {
          this.loading = false;
          this.tableData = res.data.list || [];
          this.total = res.data.count || 0;
        })
        .catch((res) => {
          this.loading = false;
          this.$message.error(res.msg || '获取数据失败');
        });
    },

    // 搜索
    handleSearch() {
      this.searchForm.page = 1;
      this.getList();
    },

    // 重置搜索
    handleReset() {
      this.searchForm = {
        class_name: '',
        page: 1,
        limit: 15,
      };
      this.getList();
    },

    // 分页变化
    handlePageChange() {
      this.getList();
    },

    // 添加商家分类
    handleAdd() {

      this.$modalForm(merchantClassifyCreateApi(0)).then(() => this.getList());
    },

    // 编辑商家分类
    handleEdit(row) {

      this.$modalForm(merchantClassifyCreateApi(row.id)).then(() => this.getList());
    },

    // 删除商家分类
    handleDelete(row, index) {

      const deleteData = {
        title: '删除商家分类',
        num: index,
        url: `merchant/classify/${row.id}`,
        method: 'DELETE',
        ids: '',
      };
      this.$modalSure(deleteData)
        .then((res) => {
          this.$message.success(res.msg || '删除成功');
          this.getList();
        })
        .catch((res) => {
          this.$message.error(res.msg || '删除失败');
        });
    },
  },
};
</script>

<style scoped lang="stylus">
.merchant-classify-manager {
  .padding-add {
    padding: 20px;
  }

  .form_content_width {
    width: 200px;
  }

  .bnt {
    margin-bottom: 14px;
  }

  .page {
    margin-top: 20px;
  }
}
</style>